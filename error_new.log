sergey@bg0smyv:~/Midlayer/p30_gitlab_midrange/midrange/tisdk/build$ MACHINE=am64xx-midrange-emmc bitbake pxc-image-base pxc-bundle-base
NOTE: Started PRServer with DBfile: /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/cache/prserv.sqlite3, Address: 127.0.0.1:45753, PID: 285246
Loading cache: 100% |##########################################################################################################################################################################| Time: 0:00:03
Loaded 10995 entries from dependency cache.
Parsing recipes: 100% |########################################################################################################################################################################| Time: 0:00:01
Parsing of 6932 .bb files complete (6931 cached, 1 parsed). 10996 targets, 1259 skipped, 8 masked, 0 errors.
NOTE: Resolving any missing task queue dependencies
NOTE: Multiple providers are available for grub-efi-native (grub-efi-native, grub-native)
Consider defining a PREFERRED_PROVIDER entry to match grub-efi-native
NOTE: Resolving any missing task queue dependencies
NOTE: Resolving any missing task queue dependencies
NOTE: Resolving any missing task queue dependencies
NOTE: Resolving any missing task queue dependencies
NOTE: Resolving any missing task queue dependencies

Build Configuration (mc:default):
BB_VERSION           = "2.8.0"
BUILD_SYS            = "x86_64-linux"
NATIVELSBSTRING      = "universal"
TARGET_SYS           = "aarch64-oe-linux"
MACHINE              = "am64xx-midrange-emmc"
DISTRO               = "arago"
DISTRO_VERSION       = "2025.0"
TUNE_FEATURES        = "aarch64"
TARGET_FPU           = ""
meta
meta-poky
meta-yocto-bsp       = "HEAD:2034fc38eb4e63984d9bd6b260aa1bf95ce562e4"
meta-networking
meta-python
meta-oe
meta-gnome
meta-filesystems
meta-multimedia
meta-webserver       = "HEAD:72018ca1b1a471226917e8246e8bbf9a374ccf97"
meta-ti-extras
meta-ti-bsp          = "HEAD:7394a1b438eaaddd4e74d354e134f9dc48d489e2"
meta-arm
meta-arm-toolchain   = "HEAD:58268ddccbe2780de28513273e15193ee949987b"
meta-tpm             = "HEAD:459d837338ca230254baa2994f870bf6eb9d0139"
meta-rauc            = "HEAD:1e3e6b334defd7fbf95cb43d23975e7b3de4b520"
meta-virtualization  = "HEAD:6f3c1d8f90947408a6587be222fec575a1ca5195"
meta-clang           = "HEAD:2b7433611d80f6d0ee1b04156fa91fc73d3c2665"
meta-hardware        = "HEAD:0b3081f9e5d2a143deda1959d03e67ed66501d3e"
meta-update
meta-pxc             = "HEAD:4b69ce42f85de22690ff885707649d2aa3db3fff"
meta-arp             = "HEAD:92992cf57c806158460982cccc252b046fbbf2e3"
meta-festo           = "main:ac673c4a9a611d4709b30f0fba258e06aac058ee"
meta-festo-midrange  = "main:c4f8b1c9e50e42612cdd067b0403fa47aff527ef"


Build Configuration:
BB_VERSION           = "2.8.0"
BUILD_SYS            = "x86_64-linux"
NATIVELSBSTRING      = "universal"
TARGET_SYS           = "arm-oe-eabi"
MACHINE              = "am64xx-midrange-emmc-k3r5"
DISTRO               = "arago"
DISTRO_VERSION       = "2025.0"
TUNE_FEATURES        = "arm armv7a vfp thumb callconvention-hard"
TARGET_FPU           = "hard"
meta
meta-poky
meta-yocto-bsp       = "HEAD:2034fc38eb4e63984d9bd6b260aa1bf95ce562e4"
meta-networking
meta-python
meta-oe
meta-gnome
meta-filesystems
meta-multimedia
meta-webserver       = "HEAD:72018ca1b1a471226917e8246e8bbf9a374ccf97"
meta-ti-extras
meta-ti-bsp          = "HEAD:7394a1b438eaaddd4e74d354e134f9dc48d489e2"
meta-arm
meta-arm-toolchain   = "HEAD:58268ddccbe2780de28513273e15193ee949987b"
meta-tpm             = "HEAD:459d837338ca230254baa2994f870bf6eb9d0139"
meta-rauc            = "HEAD:1e3e6b334defd7fbf95cb43d23975e7b3de4b520"
meta-virtualization  = "HEAD:6f3c1d8f90947408a6587be222fec575a1ca5195"
meta-clang           = "HEAD:2b7433611d80f6d0ee1b04156fa91fc73d3c2665"
meta-hardware        = "HEAD:0b3081f9e5d2a143deda1959d03e67ed66501d3e"
meta-update
meta-pxc             = "HEAD:4b69ce42f85de22690ff885707649d2aa3db3fff"
meta-arp             = "HEAD:92992cf57c806158460982cccc252b046fbbf2e3"
meta-festo           = "main:ac673c4a9a611d4709b30f0fba258e06aac058ee"
meta-festo-midrange  = "main:c4f8b1c9e50e42612cdd067b0403fa47aff527ef"

Sstate summary: Wanted 234 Local 216 Mirrors 0 Missed 18 Current 5410 (92% match, 99% complete)###############################################################################                 | ETA:  0:00:00
Initialising tasks: 100% |#####################################################################################################################################################################| Time: 0:00:13
NOTE: Executing Tasks
ERROR: arp-25.1.0-r0 do_compile: ExecutionError('/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/temp/run.do_compile.286608', 1, None, None)
ERROR: Logfile of failure stored in: /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/temp/log.do_compile.286608
Log data follows:
| DEBUG: Executing shell function do_compile
| NOTE: VERBOSE=1 cmake --build /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build --target all --
| Change Dir: '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build'
|
| Run Build Command(s): ninja -v -j 4 all
| [0/2] /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/cmake -P /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build/CMakeFiles/VerifyGlobs.cmake
| [1/5712] ccache /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/aarch64-oe-linux/aarch64-oe-linux-g++ --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot -DARP_DEVICE_CEPE -DArp_Base_Acf_EXPORTS -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_REGEX_DYN_LINK -DBOOST_REGEX_NO_LIB -DFMT_SHARED -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Libs -mbranch-protection=standard -fstack-protector-strong  -O2 -D_FORTIFY_SOURCE=2 -Wformat -Wformat-security -Werror=format-security  --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot  -O2 -pipe -g -feliminate-unused-debug-types -fcanon-prefix-map  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native=  -fvisibility-inlines-hidden -Wall -Wno-unknown-pragmas  -Wno-reorder -Wconversion -Wno-strict-aliasing -Wno-invalid-offsetof -Wno-psabi -fPIC -fno-gnu-unique -O2 -fexceptions -fnon-call-exceptions -Wformat -Wformat-security -DNDEBUG -std=c++20 -fPIC -fno-gnu-unique -fabi-version=18 -pthread -MD -MT Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o -MF Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o.d -o Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o -c /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Arp/Base/Acf/AcfLibrary.cpp
| FAILED: Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o
| ccache /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/aarch64-oe-linux/aarch64-oe-linux-g++ --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot -DARP_DEVICE_CEPE -DArp_Base_Acf_EXPORTS -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_REGEX_DYN_LINK -DBOOST_REGEX_NO_LIB -DFMT_SHARED -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Libs -mbranch-protection=standard -fstack-protector-strong  -O2 -D_FORTIFY_SOURCE=2 -Wformat -Wformat-security -Werror=format-security  --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot  -O2 -pipe -g -feliminate-unused-debug-types -fcanon-prefix-map  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native=  -fvisibility-inlines-hidden -Wall -Wno-unknown-pragmas  -Wno-reorder -Wconversion -Wno-strict-aliasing -Wno-invalid-offsetof -Wno-psabi -fPIC -fno-gnu-unique -O2 -fexceptions -fnon-call-exceptions -Wformat -Wformat-security -DNDEBUG -std=c++20 -fPIC -fno-gnu-unique -fabi-version=18 -pthread -MD -MT Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o -MF Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o.d -o Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/AcfLibrary.cpp.o -c /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Arp/Base/Acf/AcfLibrary.cpp
| In file included from ../git/Source/Arp/Base/Acf/Commons/IComponent.hpp:8,
|                  from ../git/Source/Arp/Base/Acf/Commons/IComponentFactory.hpp:8,
|                  from ../git/Source/Arp/Base/Acf/Commons/ILibrary.hpp:8,
|                  from ../git/Source/Arp/Base/Acf/Commons/LibraryBase.hpp:9,
|                  from ../git/Source/Arp/Base/Acf/AcfLibrary.hpp:8,
|                  from ../git/Source/Arp/Base/Acf/AcfLibrary.cpp:6:
| ../git/Source/Arp/Base/Acf/AcfLibrary.cpp: In constructor 'Arp::Base::Acf::AcfLibrary::AcfLibrary()':
| ../git/Source/Arp/Base/Core/ArpVersion.hpp:105:39: error: expected primary-expression before '(' token
|   105 | #define ARP_VERSION_CURRENT ArpVersion(ARP_VERSION_MAJOR, ARP_VERSION_MINOR, ARP_VERSION_PATCH, ARP_VERSION_BUILD, ARP_VERSION_STATE, ARP_VERSION_NAME)
|       |                                       ^
| ../git/Source/Arp/Base/Acf/AcfLibrary.cpp:13:19: note: in expansion of macro 'ARP_VERSION_CURRENT'
|    13 |     : LibraryBase(ARP_VERSION_CURRENT)
|       |                   ^~~~~~~~~~~~~~~~~~~
| ../git/Source/Arp/Base/Core/ArpVersion.hpp:105:114: error: expected primary-expression before ',' token
|   105 | #define ARP_VERSION_CURRENT ArpVersion(ARP_VERSION_MAJOR, ARP_VERSION_MINOR, ARP_VERSION_PATCH, ARP_VERSION_BUILD, ARP_VERSION_STATE, ARP_VERSION_NAME)
|       |                                                                                                                  ^
| ../git/Source/Arp/Base/Acf/AcfLibrary.cpp:13:19: note: in expansion of macro 'ARP_VERSION_CURRENT'
|    13 |     : LibraryBase(ARP_VERSION_CURRENT)
|       |                   ^~~~~~~~~~~~~~~~~~~
| [2/5712] ccache /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/aarch64-oe-linux/aarch64-oe-linux-g++ --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot -DARP_DEVICE_CEPE -DArp_Base_Acf_EXPORTS -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_REGEX_DYN_LINK -DBOOST_REGEX_NO_LIB -DFMT_SHARED -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Libs -mbranch-protection=standard -fstack-protector-strong  -O2 -D_FORTIFY_SOURCE=2 -Wformat -Wformat-security -Werror=format-security  --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot  -O2 -pipe -g -feliminate-unused-debug-types -fcanon-prefix-map  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native=  -fvisibility-inlines-hidden -Wall -Wno-unknown-pragmas  -Wno-reorder -Wconversion -Wno-strict-aliasing -Wno-invalid-offsetof -Wno-psabi -fPIC -fno-gnu-unique -O2 -fexceptions -fnon-call-exceptions -Wformat -Wformat-security -DNDEBUG -std=c++20 -fPIC -fno-gnu-unique -fabi-version=18 -pthread -MD -MT Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o -MF Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o.d -o Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o -c /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Arp/Base/Acf/Commons/BuildInfo/SdkBuildInfo.cpp
| FAILED: Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o
| ccache /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/aarch64-oe-linux/aarch64-oe-linux-g++ --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot -DARP_DEVICE_CEPE -DArp_Base_Acf_EXPORTS -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_REGEX_DYN_LINK -DBOOST_REGEX_NO_LIB -DFMT_SHARED -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Libs -mbranch-protection=standard -fstack-protector-strong  -O2 -D_FORTIFY_SOURCE=2 -Wformat -Wformat-security -Werror=format-security  --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot  -O2 -pipe -g -feliminate-unused-debug-types -fcanon-prefix-map  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native=  -fvisibility-inlines-hidden -Wall -Wno-unknown-pragmas  -Wno-reorder -Wconversion -Wno-strict-aliasing -Wno-invalid-offsetof -Wno-psabi -fPIC -fno-gnu-unique -O2 -fexceptions -fnon-call-exceptions -Wformat -Wformat-security -DNDEBUG -std=c++20 -fPIC -fno-gnu-unique -fabi-version=18 -pthread -MD -MT Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o -MF Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o.d -o Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/BuildInfo/SdkBuildInfo.cpp.o -c /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Arp/Base/Acf/Commons/BuildInfo/SdkBuildInfo.cpp
| In file included from ../git/Source/Arp/Base/Acf/Commons/BuildInfo/SdkBuildInfo.cpp:7:
| ../git/Source/Arp/Base/Acf/Commons/BuildInfo/SdkBuildInfo.cpp: In member function 'Arp::Base::Core::ArpVersion Arp::Base::Acf::Commons::BuildInfo::SdkBuildInfo::GetBuildVersion() const':
| ../git/Source/Arp/Base/Core/ArpVersion.hpp:113:37: error: expected primary-expression before '(' token
|   113 | #define ARP_VERSION_BUILT ArpVersion(ARP_VERSION_MAJOR, ARP_VERSION_MINOR, ARP_VERSION_PATCH, ARP_VERSION_BUILD, ARP_VERSION_STATE, ARP_VERSION_NAME)
|       |                                     ^
| ../git/Source/Arp/Base/Acf/Commons/BuildInfo/SdkBuildInfo.cpp:22:12: note: in expansion of macro 'ARP_VERSION_BUILT'
|    22 |     return ARP_VERSION_BUILT;
|       |            ^~~~~~~~~~~~~~~~~
| ../git/Source/Arp/Base/Core/ArpVersion.hpp:113:112: error: expected primary-expression before ',' token
|   113 | #define ARP_VERSION_BUILT ArpVersion(ARP_VERSION_MAJOR, ARP_VERSION_MINOR, ARP_VERSION_PATCH, ARP_VERSION_BUILD, ARP_VERSION_STATE, ARP_VERSION_NAME)
|       |                                                                                                                ^
| ../git/Source/Arp/Base/Acf/Commons/BuildInfo/SdkBuildInfo.cpp:22:12: note: in expansion of macro 'ARP_VERSION_BUILT'
|    22 |     return ARP_VERSION_BUILT;
|       |            ^~~~~~~~~~~~~~~~~
| [3/5712] ccache /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/aarch64-oe-linux/aarch64-oe-linux-g++ --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot -DARP_DEVICE_CEPE -DArp_Base_Acf_EXPORTS -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_REGEX_DYN_LINK -DBOOST_REGEX_NO_LIB -DFMT_SHARED -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Libs -mbranch-protection=standard -fstack-protector-strong  -O2 -D_FORTIFY_SOURCE=2 -Wformat -Wformat-security -Werror=format-security  --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot  -O2 -pipe -g -feliminate-unused-debug-types -fcanon-prefix-map  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native=  -fvisibility-inlines-hidden -Wall -Wno-unknown-pragmas  -Wno-reorder -Wconversion -Wno-strict-aliasing -Wno-invalid-offsetof -Wno-psabi -fPIC -fno-gnu-unique -O2 -fexceptions -fnon-call-exceptions -Wformat -Wformat-security -DNDEBUG -std=c++20 -fPIC -fno-gnu-unique -fabi-version=18 -pthread -MD -MT Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/ComponentInfoKind.cpp.o -MF Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/ComponentInfoKind.cpp.o.d -o Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/ComponentInfoKind.cpp.o -c /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Arp/Base/Acf/Commons/ComponentInfoKind.cpp
| [4/5712] ccache /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native/usr/bin/aarch64-oe-linux/aarch64-oe-linux-g++ --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot -DARP_DEVICE_CEPE -DArp_Base_Acf_EXPORTS -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_REGEX_DYN_LINK -DBOOST_REGEX_NO_LIB -DFMT_SHARED -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source -I/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Libs -mbranch-protection=standard -fstack-protector-strong  -O2 -D_FORTIFY_SOURCE=2 -Wformat -Wformat-security -Werror=format-security  --sysroot=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot  -O2 -pipe -g -feliminate-unused-debug-types -fcanon-prefix-map  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source=/usr/src/debug/arp/25.1.0  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/build=/usr/src/debug/arp/25.1.0  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fmacro-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot=  -fdebug-prefix-map=/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/recipe-sysroot-native=  -fvisibility-inlines-hidden -Wall -Wno-unknown-pragmas  -Wno-reorder -Wconversion -Wno-strict-aliasing -Wno-invalid-offsetof -Wno-psabi -fPIC -fno-gnu-unique -O2 -fexceptions -fnon-call-exceptions -Wformat -Wformat-security -DNDEBUG -std=c++20 -fPIC -fno-gnu-unique -fabi-version=18 -pthread -MD -MT Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/ComponentCategory.cpp.o -MF Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/ComponentCategory.cpp.o.d -o Arp/Base/Acf/CMakeFiles/Arp.Base.Acf.dir/Commons/ComponentCategory.cpp.o -c /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/arago-tmp/work/aarch64-oe-linux/arp/25.1.0/git/Source/Arp/Base/Acf/Commons/ComponentCategory.cpp
| ninja: build stopped: subcommand failed.
|
| WARNING: exit code 1 from a shell command.
ERROR: Task (/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/meta-arp/recipes-arp/arp/arp_git.bb:do_compile) failed with exit code '1'
NOTE: Tasks Summary: Attempted 8319 tasks of which 8318 didn't need to be rerun and 1 failed.
NOTE: Writing buildhistory
NOTE: Writing buildhistory took: 6 seconds
NOTE: Writing buildhistory
NOTE: Writing buildhistory took: 6 seconds

Summary: 1 task failed:
  /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/meta-arp/recipes-arp/arp/arp_git.bb:do_compile
Summary: There was 1 ERROR message, returning a non-zero exit code.
sergey@bg0smyv:~/Midlayer/p30_gitlab_midrange/midrange/tisdk/build$ ^C
