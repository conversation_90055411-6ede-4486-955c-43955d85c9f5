---
name: Bug report
about: Create a report to help us improve
title: ''
labels: feature-request
assignees: ''

---

<!-- Please search existing issues to avoid creating duplicates. -->
<!-- Also please test using the latest version to make sure your issue has not already been fixed ->

**Environment (please complete the following information):**
- OS and Version: [e.g. Linux Mint 19.1]
- IDE used: yes/no
  - IDE and Version [e.g. eclipse photon] 
  - used plugins: [only relevant plugins, e.g. PLCnext Technology C++ ]
- PLCnext Technology:
  - Firmware Version: [e.g. 2019.0]
  - PLCnext Engineer: [ e.g. 2019.0]


**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Additional context**
Add any other context about the problem here.
