﻿<?xml version="1.0" encoding="UTF-8"?>
<AcfConfigurationDocument
  xmlns="http://www.phoenixcontact.com/schema/acfconfig"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.phoenixcontact.com/schema/acfconfig.xsd"
  schemaVersion="1.0" >

  <Processes>
    <Process name="LIB_AxioReadProcess" settingsPath="$ARP_ACF_SETTINGS_FILE$" />
  </Processes>

  <Libraries>
    <Library name="LIB_AxioRead.LIB_AxioReadLibrary" binaryPath="/opt/plcnext/projects/Components/libLIB_AxioRead.so" />
  </Libraries>

  <Components>
    <Component name="COMP_AxioRead1" type="LIB_AxioRead::COMP_AxioRead" library="LIB_AxioRead.LIB_AxioReadLibrary" process="LIB_AxioReadProcess" >
			<Settings path="/opt/plcnext/projects/Components/AxioReadConfig.json" /> 
	</Component>
  </Components>

</AcfConfigurationDocument>