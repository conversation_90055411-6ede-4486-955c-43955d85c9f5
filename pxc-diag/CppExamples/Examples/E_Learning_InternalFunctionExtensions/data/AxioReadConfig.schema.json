{"$id": "http://json-schema.org/draft-07/schema#", "title": "Axio_Read_Config", "description": "Validation file for the JSON based configuration", "type": "object", "properties": {"Configurations": {"type": "array", "items": {"$ref": "#/definitions/Configuration"}}}, "definitions": {"Configuration": {"type": "object", "required": ["Index", "Subindex", "DisplayAsText"], "properties": {"Index": {"type": "integer", "description": "The object index"}, "Subindex": {"type": "integer", "description": "The object subindex"}, "DisplayAsText": {"type": "boolean", "description": "The output is an ASCI string"}}}}}