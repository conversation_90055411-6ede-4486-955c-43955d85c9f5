﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<OpcUAConfigurationDocument schemaVersion="2.4" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.phoenixcontact.com/schema/opcuaconfig" xmlns="http://www.phoenixcontact.com/schema/opcuaconfig">
  <NodeName>axc-f-2152-1</NodeName>
  <ServerCertificate>
    <SelfSigned>
      <SubjectAlternativeName>
        <NotSet />
      </SubjectAlternativeName>
      <SubjectAlternativeName>
        <NotSet />
      </SubjectAlternativeName>
      <SubjectAlternativeName>
        <NotSet />
      </SubjectAlternativeName>
      <SubjectAlternativeName>
        <NotSet />
      </SubjectAlternativeName>
    </SelfSigned>
  </ServerCertificate>
  <GdsPortsToProvide>
    <Flagged />
  </GdsPortsToProvide>
  <SubscriptionSettings>
    <SubscriptionKind>DirectRead</SubscriptionKind>
  </SubscriptionSettings>
  <SecuritySettings ApplicationUriCheck="true" ApplicationAuthentication="true">
    <Basic256Sha256 />
    <Aes128Sha256RsaOaep />
    <Aes256Sha256RsaPss />
  </SecuritySettings>
</OpcUAConfigurationDocument>