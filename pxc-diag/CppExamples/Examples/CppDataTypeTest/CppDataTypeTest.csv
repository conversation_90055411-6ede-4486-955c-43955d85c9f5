Group,Name,DataType,Usage,IsTranslate,Comment,InitValue,IsRetain,IsConstant,IsOpc,IsHmi,IsProficloud,Address
,inBoolean,BOOL,InputPort,False,,,False,False,False,False,False,
,inInt8,SINT,InputPort,False,,,False,False,False,False,False,
,inUint8,USINT,InputPort,False,,,False,False,False,False,False,
,inInt16,INT,InputPort,False,,,False,False,False,False,False,
,inUint16,UINT,InputPort,False,,,False,False,False,False,False,
,inInt32,DINT,InputPort,False,,,False,False,False,False,False,
,inUint32,UDINT,InputPort,False,,,False,False,False,False,False,
,inInt64,LINT,InputPort,False,,,False,False,False,False,False,
,inUint64,ULINT,InputPort,False,,,False,False,False,False,False,
,inByte,BYTE,InputPort,False,,,False,False,False,False,False,
,inWord,WORD,InputPort,False,,,False,False,False,False,False,
,inDword,DWORD,InputPort,False,,,False,False,False,False,False,
,inLword,LWORD,InputPort,False,,,False,False,False,False,False,
,inFloat32,REAL,InputPort,False,,,False,False,False,False,False,
,inFloat64,LREAL,InputPort,False,,,False,False,False,False,False,
,inString,STRING,InputPort,False,,,False,False,False,False,False,
,inWString,WSTRING,InputPort,False,,,False,False,False,False,False,
,inString420,outString420String420,InputPort,False,,,False,False,False,False,False,
,inWString420,outWString420WString420,InputPort,False,,,False,False,False,False,False,
,inStruct,CppDataTypeTest.CppDataTypeTestProgram.s_alltypes,InputPort,False,,,False,False,False,False,False,
,inarrayBoolean,ARRAY_BOOL_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayInt8,ARRAY_SINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayUint8,ARRAY_USINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayInt16,ARRAY_INT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayUint16,ARRAY_UINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayInt32,ARRAY_DINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayUint32,ARRAY_UDINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayInt64,ARRAY_LINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayUint64,ARRAY_ULINT_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayByte,byte_array,InputPort,False,,,False,False,False,False,False,
,inarrayWord,word_array,InputPort,False,,,False,False,False,False,False,
,inarrayDword,doubleword_array,InputPort,False,,,False,False,False,False,False,
,inarrayLword,Lword_array,InputPort,False,,,False,False,False,False,False,
,inarrayFloat32,ARRAY_REAL_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayFloat64,ARRAY_LREAL_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayString,ARRAY_STRING_0_9,InputPort,False,,,False,False,False,False,False,
,inarrayWString,ARRAY_WSTRING_0_9,InputPort,False,,,False,False,False,False,False,
