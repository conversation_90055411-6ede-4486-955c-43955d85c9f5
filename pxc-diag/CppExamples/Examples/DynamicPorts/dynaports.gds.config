<?xml version="1.0" encoding="utf-8"?>
<GdsConfigurationDocument xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0" xmlns="http://www.phoenixcontact.com/schema/gdsconfig">
  <ComponentTaskRelations />
  <Connectors>
    <Connector startPort="DynamicPortsComponent1/DynamicPortsProgram1.sitting_room_light" endPort="Arp.Io.AxlC/0.OUT00" />
    <Connector startPort="DynamicPortsComponent1/DynamicPortsProgram1.outside_light" endPort="Arp.Io.AxlC/0.OUT01" />
    <Connector startPort="Arp.Io.AxlC/0.IN00" endPort="DynamicPortsComponent1/DynamicPortsProgram1.outside_movement" />
    <Connector startPort="Arp.Io.AxlC/0.IN01" endPort="DynamicPortsComponent1/DynamicPortsProgram1.outside_daytime" />
  </Connectors>
</GdsConfigurationDocument>
