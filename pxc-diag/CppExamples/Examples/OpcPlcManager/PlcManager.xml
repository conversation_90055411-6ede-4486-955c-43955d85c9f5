<?xml version="1.0" encoding="utf-8"?>
<UANodeSet xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uax="http://opcfoundation.org/UA/2008/02/Types.xsd" xmlns="http://opcfoundation.org/UA/2011/03/UANodeSet.xsd" xmlns:s1="http://yourorganisation.org/MyModel/Types.xsd" xmlns:ua="http://unifiedautomation.com/Configuration/NodeSet.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <NamespaceUris>
        <Uri>http://yourorganisation.org/MyModel/</Uri>
    </NamespaceUris>
    <Models>
        <Model ModelUri="http://yourorganisation.org/MyModel/" PublicationDate="2021-03-08T12:55:53Z" Version="1.0.0">
            <RequiredModel ModelUri="http://opcfoundation.org/UA/" PublicationDate="2020-07-15T00:00:00Z" Version="1.04.7"/>
        </Model>
    </Models>
    <Aliases>
        <Alias <PERSON>as="Boolean">i=1</Alias>
        <Alias Alias="String">i=12</Alias>
        <Alias Alias="DateTime">i=13</Alias>
        <Alias Alias="Organizes">i=35</Alias>
        <Alias Alias="HasModellingRule">i=37</Alias>
        <Alias Alias="HasTypeDefinition">i=40</Alias>
        <Alias Alias="HasSubtype">i=45</Alias>
        <Alias Alias="HasProperty">i=46</Alias>
        <Alias Alias="HasComponent">i=47</Alias>
        <Alias Alias="IdType">i=256</Alias>
        <Alias Alias="NumericRange">i=291</Alias>
        <Alias Alias="Argument">i=296</Alias>
    </Aliases>
    <Extensions>
        <Extension>
            <ua:ModelInfo Tool="UaModeler" Hash="0J8XpwHcrRwuEk2U/sipgw==" Version="1.6.5"/>
        </Extension>
    </Extensions>
    <UAObjectType NodeId="ns=1;i=1002" BrowseName="1:PlcManagerType">
        <DisplayName>PlcManagerType</DisplayName>
        <References>
            <Reference ReferenceType="HasComponent">ns=1;i=7001</Reference>
            <Reference ReferenceType="HasSubtype" IsForward="false">i=58</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7003</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7004</Reference>
        </References>
    </UAObjectType>
    <UAMethod ParentNodeId="ns=1;i=1002" NodeId="ns=1;i=7001" BrowseName="1:GetPlcState">
        <DisplayName>GetPlcState</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1002</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6001</Reference>
        </References>
    </UAMethod>
    <UAVariable DataType="Argument" ParentNodeId="ns=1;i=7001" ValueRank="1" NodeId="ns=1;i=6001" ArrayDimensions="1" BrowseName="OutputArguments">
        <DisplayName>OutputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7001</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>state</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=7</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>
    <UAMethod ParentNodeId="ns=1;i=1002" NodeId="ns=1;i=7003" BrowseName="1:Start">
        <DisplayName>Start</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty">ns=1;i=6010</Reference>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1002</Reference>
        </References>
    </UAMethod>
    <UAVariable DataType="Argument" ParentNodeId="ns=1;i=7003" ValueRank="1" NodeId="ns=1;i=6010" ArrayDimensions="2" BrowseName="InputArguments">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7003</Reference>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>startKind</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=3</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>async</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=1</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>
    <UAMethod ParentNodeId="ns=1;i=1002" NodeId="ns=1;i=7004" BrowseName="1:Stop">
        <DisplayName>Stop</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty">ns=1;i=6016</Reference>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1002</Reference>
        </References>
    </UAMethod>
    <UAVariable DataType="Argument" ParentNodeId="ns=1;i=7004" ValueRank="1" NodeId="ns=1;i=6016" ArrayDimensions="1" BrowseName="InputArguments">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7004</Reference>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>async</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=1</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>
    <UAObject NodeId="ns=1;i=5002" BrowseName="1:PlcManager">
        <DisplayName>PlcManager</DisplayName>
        <References>
            <Reference ReferenceType="HasComponent">ns=1;i=7002</Reference>
            <Reference ReferenceType="Organizes" IsForward="false">i=85</Reference>
            <Reference ReferenceType="HasTypeDefinition">ns=1;i=1002</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7005</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7006</Reference>
        </References>
    </UAObject>
    <UAMethod ParentNodeId="ns=1;i=5002" NodeId="ns=1;i=7002" BrowseName="1:GetPlcState" MethodDeclarationId="ns=1;i=7001">
        <DisplayName>GetPlcState</DisplayName>
        <References>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=5002</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6002</Reference>
        </References>
        <Extensions>
            <Extension>
                <MethodTarget xmlns="http://phoenixcontact.com/OpcUA/2019/NodeSetExtensions.xsd" FunctionBlock="OpcPlcManagerComponent1/GetPlcState"/>
            </Extension>
        </Extensions>
    </UAMethod>
    <UAVariable DataType="Argument" ParentNodeId="ns=1;i=7002" ValueRank="1" NodeId="ns=1;i=6002" ArrayDimensions="1" BrowseName="OutputArguments">
        <DisplayName>OutputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7002</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>state</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=7</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>
    <UAMethod ParentNodeId="ns=1;i=5002" NodeId="ns=1;i=7005" BrowseName="1:Start" MethodDeclarationId="ns=1;i=7003">
        <DisplayName>Start</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty">ns=1;i=6013</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=5002</Reference>
        </References>
        <Extensions>
            <Extension>
                <MethodTarget xmlns="http://phoenixcontact.com/OpcUA/2019/NodeSetExtensions.xsd" FunctionBlock="OpcPlcManagerComponent1/Start"/>
            </Extension>
        </Extensions>
    </UAMethod>
    <UAVariable DataType="Argument" ParentNodeId="ns=1;i=7005" ValueRank="1" NodeId="ns=1;i=6013" ArrayDimensions="2" BrowseName="InputArguments">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7005</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>startKind</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=3</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>async</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=1</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>
    <UAMethod ParentNodeId="ns=1;i=5002" NodeId="ns=1;i=7006" BrowseName="1:Stop" MethodDeclarationId="ns=1;i=7004">
        <DisplayName>Stop</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty">ns=1;i=6017</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=5002</Reference>
        </References>
        <Extensions>
            <Extension>
                <MethodTarget xmlns="http://phoenixcontact.com/OpcUA/2019/NodeSetExtensions.xsd" FunctionBlock="OpcPlcManagerComponent1/Stop"/>
            </Extension>
        </Extensions>
    </UAMethod>
    <UAVariable DataType="Argument" ParentNodeId="ns=1;i=7006" ValueRank="1" NodeId="ns=1;i=6017" ArrayDimensions="1" BrowseName="InputArguments">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7006</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>async</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=1</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions/>
                            <uax:Description/>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>
    <UAObject SymbolicName="http___yourorganisation_org_MyModel_" NodeId="ns=1;i=5001" BrowseName="1:http://yourorganisation.org/MyModel/">
        <DisplayName>http://yourorganisation.org/MyModel/</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty">ns=1;i=6003</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=11616</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">i=11715</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6004</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6005</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6006</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6007</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6008</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6009</Reference>
        </References>
    </UAObject>
    <UAVariable DataType="Boolean" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6003" BrowseName="IsNamespaceSubset">
        <DisplayName>IsNamespaceSubset</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:Boolean>false</uax:Boolean>
        </Value>
    </UAVariable>
    <UAVariable DataType="DateTime" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6004" BrowseName="NamespacePublicationDate">
        <DisplayName>NamespacePublicationDate</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:DateTime>2021-03-08T12:55:53Z</uax:DateTime>
        </Value>
    </UAVariable>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6005" BrowseName="NamespaceUri">
        <DisplayName>NamespaceUri</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:String>http://yourorganisation.org/MyModel/</uax:String>
        </Value>
    </UAVariable>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6006" BrowseName="NamespaceVersion">
        <DisplayName>NamespaceVersion</DisplayName>
        <References>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
        </References>
        <Value>
            <uax:String>1.0.0</uax:String>
        </Value>
    </UAVariable>
    <UAVariable DataType="IdType" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6007" ArrayDimensions="0" BrowseName="StaticNodeIdTypes">
        <DisplayName>StaticNodeIdTypes</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
    </UAVariable>
    <UAVariable DataType="NumericRange" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6008" ArrayDimensions="0" BrowseName="StaticNumericNodeIdRange">
        <DisplayName>StaticNumericNodeIdRange</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
    </UAVariable>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6009" BrowseName="StaticStringNodeIdPattern">
        <DisplayName>StaticStringNodeIdPattern</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
    </UAVariable>
</UANodeSet>
