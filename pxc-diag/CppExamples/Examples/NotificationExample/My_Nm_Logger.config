<?xml version="1.0" encoding="UTF-8"?>
<NotificationLoggerConfigurationDocument
  xmlns="http://www.phoenixcontact.com/schema/notificationloggerconfiguration"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.phoenixcontact.com/schema/notificationloggerconfiguration"
  schemaVersion="1.0">

    <Archives>
        <Archive name="My_Nm_Logger">
            <Notifications>
                <Notification name="My.NameSpace" />
                <Notification name="Arp.Plc.Domain.Plc.StateChanged" />
            </Notifications>
            <Storage path="$ARP_HOME_DIR$/logs">
                <SizeLimitation>
                    <FileSizeLimitation MaxFileSize="10MB" />
                </SizeLimitation>
                <SizeReduction>
                    <DeleteOldestEntires NumberOfEntriesToDelete="16" />
                </SizeReduction>
            </Storage>
        </Archive>
    </Archives>
</NotificationLoggerConfigurationDocument >
