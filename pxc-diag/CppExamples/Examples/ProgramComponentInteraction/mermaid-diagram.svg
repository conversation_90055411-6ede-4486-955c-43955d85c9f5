<svg id="mermaid-1597236112662" width="100%" xmlns="http://www.w3.org/2000/svg" height="100%" style="max-width:773px;" viewBox="-163 -10 773 929"><style>#mermaid-1597236112662{font-family:"trebuchet ms",verdana,arial;font-size:16px;fill:#333;}#mermaid-1597236112662 .error-icon{fill:#552222;}#mermaid-1597236112662 .error-text{fill:#552222;stroke:#552222;}#mermaid-1597236112662 .edge-thickness-normal{stroke-width:2px;}#mermaid-1597236112662 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1597236112662 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1597236112662 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1597236112662 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1597236112662 .marker{fill:#333333;}#mermaid-1597236112662 .marker.cross{stroke:#333333;}#mermaid-1597236112662 svg{font-family:"trebuchet ms",verdana,arial;font-size:16px;}#mermaid-1597236112662 .actor{stroke:hsl(259.6261682243,59.7765363128%,87.9019607843%);fill:#ECECFF;}#mermaid-1597236112662 text.actor &gt; tspan{fill:black;stroke:none;}#mermaid-1597236112662 .actor-line{stroke:grey;}#mermaid-1597236112662 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:#333;}#mermaid-1597236112662 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:#333;}#mermaid-1597236112662 #arrowhead path{fill:#333;stroke:#333;}#mermaid-1597236112662 .sequenceNumber{fill:white;}#mermaid-1597236112662 #sequencenumber{fill:#333;}#mermaid-1597236112662 #crosshead path{fill:#333;stroke:#333;}#mermaid-1597236112662 .messageText{fill:#333;stroke:#333;}#mermaid-1597236112662 .labelBox{stroke:hsl(259.6261682243,59.7765363128%,87.9019607843%);fill:#ECECFF;}#mermaid-1597236112662 .labelText,#mermaid-1597236112662 .labelText &gt; tspan{fill:black;stroke:none;}#mermaid-1597236112662 .loopText,#mermaid-1597236112662 .loopText &gt; tspan{fill:black;stroke:none;}#mermaid-1597236112662 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:hsl(259.6261682243,59.7765363128%,87.9019607843%);fill:hsl(259.6261682243,59.7765363128%,87.9019607843%);}#mermaid-1597236112662 .note{stroke:#aaaa33;fill:#fff5ad;}#mermaid-1597236112662 .noteText,#mermaid-1597236112662 .noteText &gt; tspan{fill:black;stroke:none;}#mermaid-1597236112662 .activation0{fill:#f4f4f4;stroke:#666;}#mermaid-1597236112662 .activation1{fill:#f4f4f4;stroke:#666;}#mermaid-1597236112662 .activation2{fill:#f4f4f4;stroke:#666;}#mermaid-1597236112662:root{--mermaid-font-family:"trebuchet ms",verdana,arial;}#mermaid-1597236112662 sequence{fill:apa;}</style><g></g><g><line id="actor46" x1="75" y1="5" x2="75" y2="918" class="actor-line" stroke-width="0.5px" stroke="#999"></line><rect x="0" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="75" y="32.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="75" dy="0">CounterComponent</tspan></text></g><g><line id="actor47" x1="275" y1="5" x2="275" y2="918" class="actor-line" stroke-width="0.5px" stroke="#999"></line><rect x="200" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="275" y="32.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="275" dy="0">UpCounterProgram</tspan></text></g><g><line id="actor48" x1="475" y1="5" x2="475" y2="918" class="actor-line" stroke-width="0.5px" stroke="#999"></line><rect x="400" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="475" y="32.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="475" dy="0">DownCounterProgram</tspan></text></g><defs><marker id="arrowhead" refX="5" refY="2" markerWidth="6" markerHeight="4" orient="auto"><path d="M 0,0 V 4 L6,2 Z"></path></marker></defs><defs><marker id="crosshead" markerWidth="15" markerHeight="8" orient="auto" refX="16" refY="4"><path fill="black" stroke="#000000" stroke-width="1px" d="M 9,2 V 6 L16,4 Z" style="stroke-dasharray: 0, 0;"></path><path fill="none" stroke="#000000" stroke-width="1px" d="M 0,1 L 6,7 M 6,1 L 0,7" style="stroke-dasharray: 0, 0;"></path></marker></defs><defs><marker id="sequencenumber" refX="15" refY="15" markerWidth="60" markerHeight="40" orient="auto"><circle cx="15" cy="15" r="6"></circle></marker></defs><text x="175" y="80" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">create</text><line x1="75" y1="111" x2="275" y2="111" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><text x="275" y="126" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">create</text><line x1="75" y1="157" x2="475" y2="157" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><g><rect x="-100" y="167" fill="#EDF2AE" stroke="#666" width="150" height="36" rx="0" ry="0" class="note"></rect><text x="-25" y="172" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="noteText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 14px; font-weight: 400;"><tspan x="-25">Command::CountUp</tspan></text></g><text x="175" y="263" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">GetCommand</text><line x1="275" y1="294" x2="75" y2="294" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><text x="275" y="309" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">count Up</text><path d="M 275,340 C 335,330 335,370 275,360" class="messageLine0" stroke-width="2" stroke="none" style="fill: none;"></path><text x="175" y="385" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">set current State</text><line x1="275" y1="416" x2="75" y2="416" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><g><line x1="65" y1="213" x2="360" y2="213" class="loopLine"></line><line x1="360" y1="213" x2="360" y2="426" class="loopLine"></line><line x1="65" y1="426" x2="360" y2="426" class="loopLine"></line><line x1="65" y1="213" x2="65" y2="426" class="loopLine"></line><polygon points="65,213 115,213 115,226 106.6,233 65,233" class="labelBox"></polygon><text x="90" y="226" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="labelText" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">loop</text><text x="237.5" y="231" text-anchor="middle" class="loopText" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;"><tspan x="237.5">[Execute]</tspan></text></g><text x="175" y="441" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">Refresh Command</text><line x1="275" y1="472" x2="75" y2="472" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><g><rect x="-113" y="482" fill="#EDF2AE" stroke="#666" width="163" height="36" rx="0" ry="0" class="note"></rect><text x="-31" y="487" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="noteText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 14px; font-weight: 400;"><tspan x="-31">Command::CountDown</tspan></text></g><text x="275" y="578" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">get command</text><line x1="475" y1="609" x2="75" y2="609" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><text x="475" y="624" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">count Down</text><path d="M 475,655 C 535,645 535,685 475,675" class="messageLine0" stroke-width="2" stroke="none" style="fill: none;"></path><text x="275" y="700" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">Refresh State</text><line x1="475" y1="731" x2="75" y2="731" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><g><line x1="65" y1="528" x2="560" y2="528" class="loopLine"></line><line x1="560" y1="528" x2="560" y2="741" class="loopLine"></line><line x1="65" y1="741" x2="560" y2="741" class="loopLine"></line><line x1="65" y1="528" x2="65" y2="741" class="loopLine"></line><polygon points="65,528 115,528 115,541 106.6,548 65,548" class="labelBox"></polygon><text x="90" y="541" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="labelText" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">loop</text><text x="337.5" y="546" text-anchor="middle" class="loopText" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;"><tspan x="337.5">[Execute]</tspan></text></g><text x="275" y="756" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">Refresh Command</text><line x1="475" y1="787" x2="75" y2="787" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><g><rect x="-100" y="797" fill="#EDF2AE" stroke="#666" width="150" height="36" rx="0" ry="0" class="note"></rect><text x="-25" y="802" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="noteText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 14px; font-weight: 400;"><tspan x="-25">Command::CountUp</tspan></text></g><g><rect x="0" y="853" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="75" y="885.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="75" dy="0">CounterComponent</tspan></text></g><g><rect x="200" y="853" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="275" y="885.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="275" dy="0">UpCounterProgram</tspan></text></g><g><rect x="400" y="853" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="475" y="885.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="475" dy="0">DownCounterProgram</tspan></text></g></svg>